import { getDb } from "@database";
import { patient as patientSchema } from "@database/schema";
import type { GetCCPatientType } from "@type";
import { eq } from "drizzle-orm";
import { getConfig } from "./configs";

/**
 * Cloudflare Workers-compatible buffer manager
 *
 * In Cloudflare Workers, we cannot use persistent global state between requests.
 * Instead, we use KV storage for cross-request buffer management and request-scoped
 * memory for performance optimization within a single request.
 *
 * For timestamp-based buffer management, we rely on database comparison
 * which is more reliable in a stateless environment.
 */

/**
 * Request-scoped buffer for performance optimization within a single request
 * This is cleared automatically when the request ends
 */
let requestBuffer: Map<string, number> | null = null;

/**
 * Initialize request buffer for the current request
 * Should be called at the start of each request
 */
export function initializeRequestBuffer(): void {
	requestBuffer = new Map<string, number>();
}

/**
 * Clear request buffer
 * Should be called at the end of each request (optional, as it's cleared automatically)
 */
export function clearRequestBuffer(): void {
	requestBuffer = null;
}

/**
 * Checks if an event is currently in the processing buffer to prevent duplicate processing
 *
 * This function provides an asynchronous interface to check if a specific event
 * is currently being processed or was recently processed within the buffer time window.
 * It helps prevent duplicate processing of webhook events that might be sent multiple times.
 *
 * @param bufferKey - Unique key identifying the event (format: "operation:entityType:id")
 * @returns Promise resolving to true if event is in buffer (should skip), false if not in buffer (should process)
 *
 * @example
 * ```typescript
 * const bufferKey = "EntityWasCreated:Patient:123";
 * const inBuffer = await isItInBuffer(bufferKey);
 *
 * if (inBuffer) {
 *   console.log("Event already processed recently, skipping");
 *   return { success: true, message: "Duplicate event skipped" };
 * }
 *
 * // Process the event
 * await processEvent(eventData);
 * ```
 */
export function isItInBuffer(bufferKey: string): Promise<boolean> {
	return Promise.resolve(isInBufferSync(bufferKey));
}

/**
 * Synchronous version of buffer check for internal use with automatic cleanup
 *
 * Performs the actual buffer checking logic, including automatic cleanup of expired
 * entries and validation of buffer timestamps against the configured buffer duration.
 *
 * @param bufferKey - Unique key identifying the event
 * @returns True if the event is in buffer (within time window), false otherwise
 *
 * @internal This function is for internal use only. Use isItInBuffer() for public API.
 *
 * @example
 * ```typescript
 * // Internal usage only
 * const inBuffer = isInBufferSync("ProcessPatientCreate:123");
 * if (inBuffer) {
 *   // Event was processed recently
 *   return;
 * }
 * ```
 */
function isInBufferSync(bufferKey: string): boolean {
	if (!requestBuffer) {
		return false; // No buffer initialized, not in buffer
	}

	const now = Date.now();
	const bufferTime = Number(getConfig("syncBufferTimeSec")) * 1000; // Convert to milliseconds

	// Clean expired entries first
	cleanExpiredEntries(now, bufferTime);

	// Check if key exists and is still valid
	const timestamp = requestBuffer.get(bufferKey);
	if (timestamp && now - timestamp < bufferTime) {
		return true;
	}

	return false;
}

/**
 * Adds an event to the processing buffer with current timestamp
 *
 * This function marks an event as being processed by adding it to the buffer
 * with the current timestamp. This prevents duplicate processing of the same
 * event within the configured buffer time window.
 *
 * @param bufferKey - Unique key identifying the event to add to buffer
 *
 * @example
 * ```typescript
 * const bufferKey = generatePatientBufferKey("ProcessPatientCreate", patientId);
 *
 * // Check if already in buffer
 * if (await isItInBuffer(bufferKey)) {
 *   return { success: true, message: "Already processed" };
 * }
 *
 * // Add to buffer before processing
 * addToBuffer(bufferKey);
 *
 * // Process the event
 * await processPatientCreate(patientData);
 * ```
 */
export function addToBuffer(bufferKey: string): void {
	if (!requestBuffer) {
		initializeRequestBuffer();
	}
	if (requestBuffer) {
		requestBuffer.set(bufferKey, Date.now());
	}
}

/**
 * Removes an event from the processing buffer
 * This can be called when processing is complete, though it's not strictly necessary
 * as entries will expire automatically
 *
 * @param bufferKey - Unique key identifying the event
 */
export function removeFromBuffer(bufferKey: string): void {
	if (requestBuffer) {
		requestBuffer.delete(bufferKey);
	}
}

/**
 * Cleans expired entries from the buffer
 * This is called automatically during buffer checks to prevent memory leaks
 *
 * @param now - Current timestamp
 * @param bufferTime - Buffer time in milliseconds
 */
function cleanExpiredEntries(now: number, bufferTime: number): void {
	if (!requestBuffer) {
		return;
	}

	for (const [key, timestamp] of requestBuffer.entries()) {
		if (now - timestamp >= bufferTime) {
			requestBuffer.delete(key);
		}
	}
}

/**
 * Gets the current size of the processing buffer
 * Useful for monitoring and debugging
 *
 * @returns Number of entries currently in the buffer
 */
export function getBufferSize(): number {
	return requestBuffer ? requestBuffer.size : 0;
}

/**
 * Clears all entries from the processing buffer
 * Should only be used for testing or emergency cleanup
 */
export function clearBuffer(): void {
	if (requestBuffer) {
		requestBuffer.clear();
	}
}

/**
 * Gets buffer statistics for monitoring
 *
 * @returns Object containing buffer statistics
 */
export function getBufferStats(): {
	size: number;
	oldestEntry: number | null;
	newestEntry: number | null;
} {
	let oldest: number | null = null;
	let newest: number | null = null;

	if (requestBuffer) {
		for (const timestamp of requestBuffer.values()) {
			if (oldest === null || timestamp < oldest) {
				oldest = timestamp;
			}
			if (newest === null || timestamp > newest) {
				newest = timestamp;
			}
		}
	}

	return {
		size: requestBuffer ? requestBuffer.size : 0,
		oldestEntry: oldest,
		newestEntry: newest,
	};
}

/**
 * Generates a buffer key for patient operations
 *
 * @param operation - Operation type (e.g., "ProcessPatientCreate", "ProcessPatientUpdate")
 * @param patientId - Patient ID (CC ID or AP ID)
 * @returns Buffer key string
 */
export function generatePatientBufferKey(
	operation: string,
	patientId: string | number,
): string {
	return `${operation}:${patientId}`;
}

/**
 * Generates a buffer key for appointment operations
 *
 * @param operation - Operation type (e.g., "ProcessAppointmentCreate", "ProcessAppointmentUpdate")
 * @param appointmentId - Appointment ID (CC ID or AP ID)
 * @returns Buffer key string
 */
export function generateAppointmentBufferKey(
	operation: string,
	appointmentId: string | number,
): string {
	return `${operation}:${appointmentId}`;
}

/**
 * Generates a buffer key for invoice/payment operations
 *
 * @param operation - Operation type (e.g., "ProcessInvoicePayment")
 * @param entityId - Entity ID
 * @returns Buffer key string
 */
export function generateInvoicePaymentBufferKey(
	operation: string,
	entityId: string | number,
): string {
	return `${operation}:${entityId}`;
}

/**
 * Helper function to check and add to buffer in one operation
 * This is the most common pattern - check if in buffer, and if not, add it
 *
 * @param bufferKey - Unique key identifying the event
 * @returns True if the event was already in buffer (should skip), false if added to buffer (should process)
 */
export function checkAndAddToBuffer(bufferKey: string): boolean {
	if (isInBufferSync(bufferKey)) {
		return true; // Already in buffer, should skip
	}

	addToBuffer(bufferKey);
	return false; // Not in buffer, added now, should process
}

/**
 * Timestamp-based buffer check for patient data with multi-criteria search
 *
 * This method implements the required CC webhook flow:
 * 1. Extract timestamps from CC webhook payload (createdAt, updatedAt)
 * 2. Check for existing patient with timestamp validation (±1 minute tolerance)
 *    - Search by ccId, email, phone (in order of preference)
 *    - Compare payload timestamps with local ccUpdatedAt field
 *    - Skip processing if within 1-minute tolerance
 *
 * @param payload - Patient payload from CC webhook
 * @param patient - Local patient record (optional, will be fetched if not provided)
 * @returns True if within buffer tolerance (should skip), false if outside buffer (should process)
 */
export async function isInBuffer(
	payload: GetCCPatientType,
	patient?: { ccUpdatedAt: string | null },
): Promise<boolean> {
	const payloadUpdatedAt = payload.updatedAt;
	let localUpdatedAt = patient?.ccUpdatedAt;

	// If no patient provided, search for existing patient using multi-criteria search
	if (!patient) {
		try {
			const db = getDb();
			let dbPatient: { ccUpdatedAt: Date | null } | undefined;

			// Priority 1: Search by CC ID
			if (payload.id) {
				const results = await db
					.select({ ccUpdatedAt: patientSchema.ccUpdatedAt })
					.from(patientSchema)
					.where(eq(patientSchema.ccId, payload.id))
					.limit(1);
				dbPatient = results[0];
			}

			// Priority 2: Search by email if not found by CC ID
			if (!dbPatient && payload.email) {
				const results = await db
					.select({ ccUpdatedAt: patientSchema.ccUpdatedAt })
					.from(patientSchema)
					.where(eq(patientSchema.email, payload.email))
					.limit(1);
				dbPatient = results[0];
			}

			// Priority 3: Search by phone if not found by CC ID or email
			if (!dbPatient && payload.phoneMobile) {
				const results = await db
					.select({ ccUpdatedAt: patientSchema.ccUpdatedAt })
					.from(patientSchema)
					.where(eq(patientSchema.phone, payload.phoneMobile))
					.limit(1);
				dbPatient = results[0];
			}

			localUpdatedAt = dbPatient?.ccUpdatedAt?.toISOString() || null;
		} catch (error) {
			console.error("Error fetching patient for buffer check:", error);
			return false; // If we can't fetch, assume not in buffer
		}
	}

	// If no local timestamp exists, not in buffer
	if (!localUpdatedAt) {
		return false;
	}

	// Convert payload timestamp to Date object
	const payloadDate = new Date(payloadUpdatedAt);
	const localDate = new Date(localUpdatedAt);

	// Calculate the absolute difference in milliseconds
	const timeDifferenceMs = Math.abs(
		payloadDate.getTime() - localDate.getTime(),
	);

	// Get configurable buffer time from config and convert to milliseconds
	const bufferTimeMs = Number(getConfig("syncBufferTimeSec")) * 1000;

	// Return true if within buffer tolerance, false if outside buffer
	return timeDifferenceMs <= bufferTimeMs;
}
