import { searchCreateOrUpdatePatient } from "@storage/patientStorage";
import type { GetCCPatientType, WebhookContext } from "@type";
import {
	isInBuffer,
} from "@utils/bufferManager";
import { logSyncError } from "@utils/errorLogger";
import { determinePatientSyncAction } from "../helpers/contextAwareSync";
import { executePatientSync } from "../helpers/syncActions";

/**
 * Processes patient creation events from CC (CliniCore)
 *
 * This function handles the creation of new patients in the CC system and synchronizes
 * them to the AP (AutoPatient) platform. It implements the same business logic as the
 * ProcessPatientCreate job from v3Integration but with improved error handling and
 * performance optimizations.
 *
 * **Business Logic Flow (CC Webhook Implementation):**
 * 1. Extract timestamps from CC webhook payload (createdAt, updatedAt)
 * 2. Check for existing patient with timestamp validation (±1 minute tolerance)
 *    - Search by ccId, email, phone (in order of preference)
 *    - Compare payload timestamps with local ccUpdatedAt field
 *    - Skip processing if within 1-minute tolerance
 * 3. Validate required patient data (email or phone must be present)
 * 4. Upsert local database: Create or update patient record with CC data
 * 5. Upsert AutoPatient (AP): Sync patient data to AP system
 * 6. Sync custom fields: Ensure custom field mappings between CC and AP
 *
 * **Timestamp-Based Buffer Management:**
 * Uses timestamp comparison to prevent duplicate processing of the same patient
 * creation event within a 1-minute tolerance window (configurable via syncBufferTimeSec).
 *
 * **Error Handling:**
 * - Validates email/phone requirements before processing
 * - Logs sync errors to database for monitoring
 * - Returns detailed error messages for debugging
 * - Gracefully handles API failures and database errors
 *
 * @param payload - Complete patient data from CC webhook event
 * @param payload.id - Unique CC patient ID
 * @param payload.email - Patient email address (required if phone is empty)
 * @param payload.phoneMobile - Patient mobile phone (required if email is empty)
 * @param payload.firstName - Patient first name
 * @param payload.lastName - Patient last name
 * @param payload.updatedAt - Timestamp of last update in CC
 * @param context - Webhook processing context containing request metadata
 * @param context.requestId - Unique request identifier for tracking
 * @param context.processedAt - Timestamp when processing started
 * @param context.event - Original webhook event data
 *
 * @returns Promise resolving to processing result object
 * @returns result.success - Boolean indicating if processing was successful
 * @returns result.message - Descriptive message about the processing outcome
 * @returns result.patientId - AP contact ID if creation was successful
 *
 * @throws {Error} When critical errors occur that prevent processing
 *
 * @example
 * ```typescript
* const webhookPayload = {
 *   id: 12345,
 *   firstName: "John",
 *   lastName: "Doe",
 *   email: "<EMAIL>",
 *   phoneMobile: "+**********",
 *   updatedAt: "2024-01-01T12:00:00Z"
 * };
 *
 * const context = {
 *   requestId: "req_123",
 *   processedAt: new Date(),
 *   event: originalWebhookEvent
 * };
 *
 * const result = await processPatientCreate(webhookPayload, context);
 *
 * if (result.success) {
 *   console.log(`Patient created successfully: ${result.patientId}`);
 * } else {
 *   console.error(`Patient creation failed: ${result.message}`);
 * }
 *
```
 *
 * @see {@link processPatientUpdate} for handling patient updates
 * @see {@link determinePatientSyncAction} for context-aware sync decisions
 * @see {@link executePatientSync} for context-aware sync execution
 * @see {@link generatePatientBufferKey} for buffer key generation
 *
 * @since 1.0.0
 * @version 1.0.0
 */
export async function processPatientCreate(
	payload: GetCCPatientType,
	_context: WebhookContext,
): Promise<{ success: boolean; message: string; patientId?: string }> {
	try {
		console.log(`Processing patient create for CC ID: ${payload.id}`);

		// 1. Extract timestamps and check for existing patient with timestamp validation
		// This implements the required flow: extract timestamps → check existing → validate timestamps
		const inBuffer = await isInBuffer(payload);
		if (inBuffer) {
			const message = `Patient creation skipped - within 1-minute timestamp tolerance. CC ID: ${payload.id}, Updated: ${payload.updatedAt}`;
			console.log(message);
			return {
				success: true,
				message,
			};
		}

		// 2. Validate required data
		if (!payload?.email && !payload?.phoneMobile) {
			const message = `Email and phone are empty, dropping create patient request. CC ID: ${payload?.id}`;
			console.log(message);
			return {
				success: false,
				message,
			};
		}

		// 3. Upsert local database
		const dbPatient = await searchCreateOrUpdatePatient({
			ccId: payload.id,
			ccData: payload,
			email: payload.email || undefined,
			phone: payload.phoneMobile || undefined,
			source: "cc",
		});

		// Check if patient already has a real AP ID (not temp/pending placeholder)
		const hasRealApId = dbPatient.apId &&
			!dbPatient.apId.startsWith('pending_') &&
			!dbPatient.apId.startsWith('temp_');

		if (hasRealApId) {
			// Patient already exists in AP, no need to sync again
			console.log(`Patient already exists in AP, ID: ${dbPatient.apId}`);
			return {
				success: true,
				message: `Patient already exists in AP, ID: ${dbPatient.apId}`,
				patientId: dbPatient.apId || undefined,
			};
		}

		// 4. Upsert AutoPatient (AP) - sync patient data to AP system
		console.log(`🔄 Syncing patient to AP for CC ID: ${payload.id}`);

		try {
			// Execute patient sync to AP (includes custom field sync)
			const syncResult = await executePatientSync(
				dbPatient,
				payload,
				"create", // Force create action since we know patient doesn't exist in AP
				"ap",
			);

			if (syncResult.success) {
				console.log(
					`Patient sync completed successfully. CC ID: ${payload.id}, AP ID: ${syncResult.recordId}`,
				);

				return {
					success: true,
					message: `Patient successfully synced to AP. AP ID: ${syncResult.recordId}`,
					patientId: syncResult.recordId?.toString(),
				};
			} else {
				await logSyncError(
					"PATIENT_CREATE_SYNC_FAILED",
					new Error(syncResult.message),
					payload.id,
					"CC",
					"PatientProcessor",
				);

				return {
					success: false,
					message: `Failed to sync patient to AP: ${syncResult.message}`,
				};
			}
		} catch (syncError) {
			await logSyncError(
				"PATIENT_CREATE_SYNC_ERROR",
				syncError,
				payload.id,
				"CC",
				"PatientProcessor",
			);

			return {
				success: false,
				message: `Error during patient sync: ${syncError instanceof Error ? syncError.message : String(syncError)}`,
			};
		}
	} catch (error) {
		await logSyncError(
			"PATIENT_CREATE_ERROR",
			error,
			payload.id,
			"CC",
			"PatientProcessor",
		);

		throw error;
	}
}

/**
 * Processes patient update events from CC (CliniCore)
 *
 * This function handles updates to existing patients in the CC system and synchronizes
 * the changes to the AP (AutoPatient) platform. It implements the same business logic
 * as the ProcessPatientUpdate job from v3Integration with enhanced error handling.
 *
 * **Business Logic Flow (CC Webhook Implementation):**
 * 1. Extract timestamps from CC webhook payload (createdAt, updatedAt)
 * 2. Check for existing patient with timestamp validation (±1 minute tolerance)
 *    - Search by ccId, email, phone (in order of preference)
 *    - Compare payload timestamps with local ccUpdatedAt field
 *    - Skip processing if within 1-minute tolerance
 * 3. Validate required patient data (email or phone must be present)
 * 4. Upsert local database: Create or update patient record with CC data
 * 5. Upsert AutoPatient (AP): Sync patient data to AP system
 * 6. Sync custom fields: Ensure custom field mappings between CC and AP
 *
 * **Update vs Create Logic:**
 * If the patient doesn't exist locally, this function will create a new record
 * and sync it to AP. The upsert logic handles both scenarios seamlessly.
 *
 * **Data Validation:**
 * - Requires either email or phone to be present (not both empty)
 * - Validates CC patient ID exists
 * - Ensures data integrity before AP synchronization
 *
 * **Timestamp-Based Buffer Management:**
 * Uses timestamp comparison to prevent duplicate processing of the same patient
 * update event within a 1-minute tolerance window (configurable via syncBufferTimeSec).
 *
 * @param payload - Updated patient data from CC webhook event
 * @param payload.id - Unique CC patient ID
 * @param payload.email - Updated patient email address
 * @param payload.phoneMobile - Updated patient mobile phone
 * @param payload.firstName - Updated patient first name
 * @param payload.lastName - Updated patient last name
 * @param payload.updatedAt - Timestamp of the update in CC
 * @param context - Webhook processing context
 * @param context.requestId - Unique request identifier for tracking
 * @param context.processedAt - Timestamp when processing started
 * @param context.event - Original webhook event data
 *
 * @returns Promise resolving to processing result object
 * @returns result.success - Boolean indicating if update was successful
 * @returns result.message - Descriptive message about the update outcome
 * @returns result.patientId - AP contact ID if update was successful
 *
 * @throws {Error} When critical errors occur that prevent processing
 *
 * @example
 * ```typescript
* const updatePayload = {
 *   id: 12345,
 *   firstName: "John",
 *   lastName: "Smith", // Updated last name
 *   email: "<EMAIL>", // Updated email
 *   phoneMobile: "+**********",
 *   updatedAt: "2024-01-02T12:00:00Z"
 * };
 *
 * const result = await processPatientUpdate(updatePayload, context);
 *
 * if (result.success) {
 *   console.log(`Patient updated successfully: ${result.patientId}`);
 * } else {
 *   console.error(`Patient update failed: ${result.message}`);
 * }
 *
```
 *
 * @see {@link processPatientCreate} for handling new patient creation
 * @see {@link determinePatientSyncAction} for context-aware sync decisions
 * @see {@link executePatientSync} for context-aware sync execution
 * @see {@link generatePatientBufferKey} for buffer key generation
 *
 * @since 1.0.0
 * @version 1.0.0
 */
export async function processPatientUpdate(
	payload: GetCCPatientType,
	_context: WebhookContext,
): Promise<{ success: boolean; message: string; patientId?: string }> {
	try {
		console.log(`Processing patient update for CC ID: ${payload.id}`);

		// 1. Extract timestamps and check for existing patient with timestamp validation
		// This implements the required flow: extract timestamps → check existing → validate timestamps
		const inBuffer = await isInBuffer(payload);
		if (inBuffer) {
			const message = `Patient update skipped - within 1-minute timestamp tolerance. CC ID: ${payload.id}, Updated: ${payload.updatedAt}`;
			console.log(message);
			return {
				success: true,
				message,
			};
		}

		// 2. Validate required data
		if (!payload?.email && !payload?.phoneMobile) {
			const message = `Email and phone is empty, Dropping update patient request, Patient ID: ${payload.id}`;
			console.log(message);
			return {
				success: false,
				message,
			};
		}

		// 3. Upsert local database
		const dbPatient = await searchCreateOrUpdatePatient({
			ccId: payload.id,
			ccData: payload,
			email: payload.email || undefined,
			phone: payload.phoneMobile || undefined,
			source: "cc",
		});

		// 4. Upsert AutoPatient (AP) - sync patient data to AP system
		console.log(`🔄 Syncing patient update to AP for CC ID: ${payload.id}`);

		// 5. Execute patient sync to AP (includes custom field sync)
		const syncResult = await executePatientSync(
			dbPatient,
			payload,
			"update", // Force update action to match v3Integration behavior
			"ap",
		);

		// Handle sync result
		if (syncResult.success) {
			console.log(
				`Patient sync completed successfully. CC ID: ${payload.id}, Result: ${syncResult.message}`,
			);

			return {
				success: true,
				message: syncResult.message,
				patientId: syncResult.recordId?.toString(),
			};
		} else {
			await logSyncError(
				"PATIENT_UPDATE_CONTEXT_AWARE_SYNC_FAILED",
				new Error(syncResult.message),
				payload.id,
				"CC",
				"PatientProcessor",
			);

			return {
				success: false,
				message: `Failed to sync patient to AP: ${syncResult.message}`,
			};
		}
	} catch (error) {
		await logSyncError(
			"PATIENT_UPDATE_ERROR",
			error,
			payload.id,
			"CC",
			"PatientProcessor",
		);

		throw error;
	}
}

/**
 * Legacy function - DEPRECATED
 *
 * This function has been replaced by the context-aware sync utilities:
 * - determinePatientSyncAction() for intelligent sync decision logic
 * - executePatientSync() for context-aware sync execution
 *
 * The new approach provides:
 * - Smart sync decisions based on existing database state
 * - Prevention of unnecessary API calls for already-synced records
 * - Search-then-create logic for missing records
 * - Improved performance and data consistency
 *
 * @deprecated Use determinePatientSyncAction() and executePatientSync() instead
 * @see {@link determinePatientSyncAction} for context-aware sync decisions
 * @see {@link executePatientSync} for context-aware sync execution
 */
